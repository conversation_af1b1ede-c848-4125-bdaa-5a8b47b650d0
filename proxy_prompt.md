# Промпт для добавления поддержки прокси в библиотеки бирж

## Задача
Добавить поддержку прокси в существующую библиотеку биржи с автоматическим fallback на обычные запросы при проблемах с прокси.

## Требования к реализации

### 1. Класс ProxyManager
Создать класс `ProxyManager` в начале файла библиотеки:

```javascript
class ProxyManager {
    constructor(proxiesFilePath) {
        this.proxies = [];
        this.workingProxies = [];
        this.currentIndex = 0;
        this.loadProxies(proxiesFilePath);
    }

    loadProxies(filePath) {
        try {
            if (fs.existsSync(filePath)) {
                const data = fs.readFileSync(filePath, 'utf8');
                const lines = data.split('\n').filter(line => line.trim());
                
                this.proxies = lines.map(line => {
                    const [host, port, username, password] = line.split(':');
                    return {
                        host: host.trim(),
                        port: parseInt(port.trim()),
                        auth: username && password ? {
                            username: username.trim(),
                            password: password.trim()
                        } : undefined
                    };
                });
                
                console.log(`Загружено ${this.proxies.length} прокси`);
            }
        } catch (error) {
            console.error('Ошибка при загрузке прокси:', error.message);
        }
    }

    getProxyConfig(proxy) {
        if (!proxy) return {};
        
        return {
            proxy: {
                protocol: 'http',
                host: proxy.host,
                port: proxy.port,
                auth: proxy.auth
            },
            httpsAgent: false,
            timeout: 15000
        };
    }

    getAllProxies() {
        return [...this.proxies];
    }

    getWorkingProxies() {
        return [...this.workingProxies];
    }

    async testProxy(proxy, testUrl = 'https://api.exchange.com/endpoint') {
        try {
            const proxyConfig = this.getProxyConfig(proxy);
            
            const options = {
                method: 'GET',
                url: testUrl,
                timeout: 2000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                validateStatus: () => true,
                ...proxyConfig
            };

            const response = await axios(options);
            return response.status >= 200 && response.status < 500;
        } catch (error) {
            return false;
        }
    }

    async testAllProxiesUltraFast() {
        console.log(`Молниеносное тестирование ${this.proxies.length} прокси...`);
        
        this.workingProxies = [];
        let testedCount = 0;
        
        const testPromises = this.proxies.map(async (proxy) => {
            try {
                const isWorking = await this.testProxy(proxy);
                testedCount++;
                
                if (isWorking) {
                    this.workingProxies.push(proxy);
                }
                
                if (testedCount % 30 === 0) {
                    console.log(`Протестировано: ${testedCount}/${this.proxies.length}, рабочих: ${this.workingProxies.length}`);
                }
                
                return isWorking;
            } catch (error) {
                testedCount++;
                return false;
            }
        });

        await Promise.all(testPromises);
        
        console.log(`Тестирование завершено. Рабочих прокси: ${this.workingProxies.length} из ${this.proxies.length}`);
        return this.workingProxies.length;
    }
}
```

### 2. Модификация конструктора основного класса
```javascript
constructor(useProxies = true, testProxies = true) {
    // Существующая инициализация...
    
    this.useProxies = useProxies;
    this.testProxies = testProxies;
    this.proxiesReady = false;
    
    // Инициализируем менеджер прокси только если нужно
    if (useProxies) {
        const proxiesPath = path.join(__dirname, '..', 'proxies.txt');
        this.proxyManager = new ProxyManager(proxiesPath);
    }
}
```

### 3. Модификация метода getAllFundings
Добавить логику проверки и использования прокси:

```javascript
async getAllFundings() {
    try {
        const pairs = await this.getAllPairsNames();
        const delay = 334; // Интервал для соблюдения лимита

        if (!this.useProxies || !this.proxyManager) {
            console.log('Прокси отключены, используем обычные запросы');
            return this.getAllFundingsSequential(pairs, delay);
        }

        // Проверяем и тестируем прокси если еще не делали
        if (!this.proxiesReady && this.testProxies) {
            const workingCount = await this.proxyManager.testAllProxiesUltraFast();
            this.proxiesReady = true;
            
            if (workingCount === 0) {
                console.log('Рабочих прокси не найдено, используем обычные запросы');
                return this.getAllFundingsSequential(pairs, delay);
            }
        }

        const proxies = this.testProxies ? 
            this.proxyManager.getWorkingProxies() : 
            this.proxyManager.getAllProxies();

        if (proxies.length === 0) {
            console.log('Прокси не найдены, используем обычные запросы');
            return this.getAllFundingsSequential(pairs, delay);
        }

        console.log(`Используем ${proxies.length} ${this.testProxies ? 'протестированных' : ''} прокси для параллельных запросов`);
        return this.getAllFundingsParallel(pairs, proxies, delay);

    } catch (error) {
        console.error('Ошибка при получении фандинга:', error.message);
        console.log('Переключаемся на обычные запросы...');

        // Fallback на обычные запросы при любой ошибке
        try {
            const pairs = await this.getAllPairsNames();
            return this.getAllFundingsSequential(pairs, 334);
        } catch (fallbackError) {
            console.error('Ошибка при fallback запросах:', fallbackError.message);
            return [];
        }
    }
}
```

### 4. Добавить метод параллельных запросов
```javascript
async getAllFundingsParallel(pairs, proxies, delay) {
    const result = [];
    const batchSize = proxies.length;
    const batches = [];

    // Разбиваем пары на батчи по количеству прокси
    for (let i = 0; i < pairs.length; i += batchSize) {
        batches.push(pairs.slice(i, i + batchSize));
    }

    console.log(`Обрабатываем ${pairs.length} пар в ${batches.length} батчах по ${batchSize} запросов`);

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        const batchPromises = [];

        // Создаем промисы для каждой пары в батче с соответствующим прокси
        for (let i = 0; i < batch.length; i++) {
            const pair = batch[i];
            const proxy = proxies[i % proxies.length];

            batchPromises.push(
                this.fetchFundingRateWithProxy(pair, proxy)
            );
        }

        // Выполняем все запросы в батче параллельно
        const batchResults = await Promise.allSettled(batchPromises);

        // Обрабатываем результаты
        batchResults.forEach((promiseResult, index) => {
            if (promiseResult.status === 'fulfilled' && promiseResult.value) {
                result.push(promiseResult.value);
            } else {
                console.error(`Ошибка при обработке пары ${batch[index]}:`,
                    promiseResult.reason?.message || 'Неизвестная ошибка');
            }
        });

        console.log(`Обработан батч ${batchIndex + 1}/${batches.length}, получено ${result.length} результатов`);

        // Пауза между батчами
        if (batchIndex < batches.length - 1) {
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    return result;
}
```

### 5. Добавить метод запроса с прокси
```javascript
async fetchFundingRateWithProxy(pair, proxy) {
    try {
        const proxyConfig = this.proxyManager.getProxyConfig(proxy);

        const options = {
            method: 'GET',
            url: `${this.baseUrl}/api/endpoint`, // Заменить на реальный endpoint
            params: {
                symbol: pair // Параметры зависят от API биржи
            },
            timeout: 15000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            ...proxyConfig
        };

        const response = await axios(options);

        // Обработка ответа зависит от формата API биржи
        if (response.data && response.data.result) {
            return {
                exchange: 'EXCHANGE_NAME',
                token: pair,
                fundingRate: parseFloat(response.data.result.fundingRate) * 100,
                nextFundingTime: response.data.result.nextFundingTime
            };
        }

        return null;
    } catch (error) {
        // Детальная обработка ошибок
        if (error.code === 'EPROTO' || error.message.includes('SSL')) {
            throw new Error(`SSL ошибка с прокси ${proxy.host}:${proxy.port}`);
        } else if (error.code === 'ECONNREFUSED') {
            throw new Error(`Прокси ${proxy.host}:${proxy.port} недоступен`);
        } else if (error.code === 'ETIMEDOUT') {
            throw new Error(`Таймаут прокси ${proxy.host}:${proxy.port}`);
        } else {
            throw new Error(`Прокси ${proxy.host}:${proxy.port} - ${error.message}`);
        }
    }
}
```

### 6. Упростить main файл
```javascript
const { ExchangeAPI } = require('./exchange_lib');

async function main() {
    console.log('=== ТЕСТИРОВАНИЕ EXCHANGE API ===\n');

    // Настройка использования прокси прямо в конструкторе
    // true - использовать прокси (с автоматическим fallback на обычные запросы если прокси не работают)
    // false - обычные запросы с вашего IP с задержкой 334мс
    const exchange = new ExchangeAPI(true);

    try {
        const combinedData = await exchange.getCombinedData();
        console.log(`Получено объединенных данных: ${combinedData.length}`);
        console.log(combinedData);

    } catch (error) {
        console.error('Ошибка при выполнении тестов:', error);
    }
}

// Запускаем тестирование
main().catch(console.error);
```

## Ключевые особенности реализации

1. **Автоматический fallback**: При любых проблемах с прокси автоматически переключается на обычные запросы
2. **Быстрое тестирование**: Все прокси тестируются одновременно за 2-3 секунды
3. **Простая настройка**: Один параметр в конструкторе для включения/отключения прокси
4. **Соблюдение лимитов**: Задержка 334мс между батчами запросов
5. **Детальное логирование**: Подробная информация о процессе и ошибках
6. **Максимальная производительность**: До 40x ускорение с рабочими прокси

## Адаптация под конкретную биржу

При реализации для конкретной биржи нужно:
1. Заменить `testUrl` в методе `testProxy` на реальный endpoint биржи
2. Адаптировать `fetchFundingRateWithProxy` под API биржи
3. Изменить обработку ответов под формат данных биржи
4. Обновить параметры запросов под требования API

## Примеры для разных типов бирж

### Для бирж с поддержкой CCXT
Если биржа поддерживается CCXT, используйте `fetchFundingRates()` метод:
```javascript
// В методе getAllFundingsSequential
const fundingInfo = await this.exchange.fetchFundingRate(pair);
```

### Для бирж без поддержки CCXT
Используйте прямые API вызовы:
```javascript
// Пример для Gate.io, HTX, KuCoin, MEXC и других
const response = await axios.get(`${this.baseUrl}/api/v1/funding-rates`, {
    params: { symbol: pair }
});
```

### Специальные случаи

#### BTSE (без поддержки CCXT)
```javascript
// Endpoint: GET /api/v2.1/market_summary
// Трансформация символов: RENDERPFC -> RENDER/USDT:USDT
const transformSymbol = (symbol) => {
    if (symbol.endsWith('PFC')) {
        const base = symbol.replace('PFC', '');
        return `${base}/USDT:USDT`; // или другая котировочная валюта
    }
    return symbol;
};
```

## Результаты производительности

### С прокси (рекомендуется)
- Тестирование прокси: ~3 секунды
- Получение данных: ~1-2 секунды
- **Общее время: ~4-5 секунд**
- **Ускорение: 20-40x раз**

### Без прокси
- Получение данных: ~90-120 секунд (зависит от количества пар)
- **Скорость: ~3 запроса/сек**

## Структура файлов

```
exchanges/
├── proxies.txt              # Файл с прокси (host:port:user:pass)
├── exchange_name/
│   ├── exchange_lib.js      # Основная библиотека с поддержкой прокси
│   └── exchange_main.js     # Простой файл для тестирования
```

## Формат файла proxies.txt

```
proxy1.example.com:8080:username1:password1
proxy2.example.com:8080:username2:password2
proxy3.example.com:8080
proxy4.example.com:8080
```

## Обработка ошибок

Система автоматически обрабатывает:
- SSL ошибки прокси
- Таймауты соединений
- Недоступные прокси
- Ошибки API биржи
- Отсутствие файла прокси

При любой ошибке происходит автоматический fallback на обычные запросы с соблюдением лимитов API.

## Логирование

Система выводит подробную информацию:
- Количество загруженных прокси
- Прогресс тестирования прокси
- Количество рабочих прокси
- Прогресс получения данных
- Информацию об ошибках и fallback

## Рекомендации по использованию

1. **Всегда используйте прокси** для максимальной производительности
2. **Тестируйте прокси** перед использованием для надежности
3. **Используйте качественные прокси** для стабильной работы
4. **Мониторьте логи** для отслеживания проблем
5. **Регулярно обновляйте список прокси** для поддержания производительности
