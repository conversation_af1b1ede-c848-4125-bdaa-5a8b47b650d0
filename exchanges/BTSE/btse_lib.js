const axios = require('axios');

class BtseAPI {
    constructor() {
        this.BASE_URL = 'https://api.btse.com/futures';
    }

    //Получаем все фандинг-рейты с биржи BTSE
 
    async getAllFundings() {
        try {
            const response = await axios.get(`${this.BASE_URL}/api/v2.1/market_summary?listFullAttributes=true`);
            const markets = response.data;

            const result = [];

            for (const market of markets) {
                // Определяем фьючерсы по наличию contractSize и openInterest
                const isFutures = market.contractSize !== undefined && market.openInterest !== undefined;

                // Фильтруем только фьючерсы с фандинг-рейтом
                if (isFutures && market.fundingRate !== undefined) {
                    result.push({
                        exchange: 'BTSE',
                        token: market.symbol,
                        fundingRate: market.fundingRate * 100, // Конвертируем в проценты
                        nextFundingTime: market.fundingTime || null
                    });
                }
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении фандинга с BTSE:', error.message);
            return [];
        }
    }

 
    //Получает цены фьючерсов с биржи BTSE
     
    async getAllFuturesPrices() {
        try {
            const response = await axios.get(`${this.BASE_URL}/api/v2.1/market_summary?listFullAttributes=true`);
            const markets = response.data;

            const result = [];

            for (const market of markets) {
                // Определяем фьючерсы по наличию contractSize и openInterest
                const isFutures = market.contractSize !== undefined && market.openInterest !== undefined;

                // Фильтруем только активные фьючерсы
                if (isFutures && market.active) {
                    result.push({
                        exchange: 'BTSE',
                        token: market.symbol,
                        lastPrice: market.last,
                        markPrice: market.last, // BTSE не предоставляет отдельную mark price
                        indexPrice: market.last, // BTSE не предоставляет отдельную index price
                        dailyChange: null, // Можно вычислить если нужно
                        dailyChangePercent: market.percentageChange,
                        volume24h: market.volume,
                        quoteVolume24h: null,
                        high24h: market.high24Hr,
                        low24h: market.low24Hr,
                        bid: market.highestBid,
                        ask: market.lowestAsk,
                        timestamp: Date.now(),
                        openInterest: market.openInterest,
                        openInterestUSD: market.openInterestUSD
                    });
                }
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении цен фьючерсов с BTSE:', error.message);
            return [];
        }
    }


    // Получаем объединенные данные о фандинге и ценах фьючерсов
    async getCombinedData() {
        try {
            const [fundings, prices] = await Promise.all([
                this.getAllFundings(),
                this.getAllFuturesPrices()
            ]);

            console.log(`Получено данных о фандингах: ${fundings.length}`);
            console.log(`Получено данных о ценах: ${prices.length}`);

            // Создаем Map с данными о ценах по токенам
            const pricesMap = new Map();
            prices.forEach(price => {
                pricesMap.set(price.token, price);
            });

            // Объединяем данные
            const combinedData = [];
            let matchedCount = 0;

            fundings.forEach(funding => {
                const priceData = pricesMap.get(funding.token);
                if (priceData) {
                    combinedData.push({
                        ...funding,
                        ...priceData
                    });
                    matchedCount++;
                } else {
                    // Добавляем данные о фандинге даже если нет цены
                    combinedData.push(funding);
                }
            });

            console.log(`Совпадений по токенам: ${matchedCount} из ${fundings.length}`);
            console.log(`Процент совпадений: ${((matchedCount / fundings.length) * 100).toFixed(1)}%`);

            return combinedData;
        } catch (error) {
            console.error('Ошибка при получении объединенных данных с BTSE:', error.message);
            return [];
        }
    }
}

module.exports = { BtseAPI };