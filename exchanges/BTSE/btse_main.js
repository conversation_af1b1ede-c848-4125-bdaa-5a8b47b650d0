const { BtseAPI } = require('./btse_lib');

async function testBtseAPI() {
    const btse = new BtseAPI();
    
    try {
        
        // const fundings = await btse.getAllFundings();
        // console.log(`Получено фандинг-рейтов: ${fundings.length}`);
        // console.log(fundings);


        // const prices = await btse.getAllFuturesPrices();
        // console.log(`Получено цен фьючерсов: ${prices.length}`);
        // console.log(prices);

        
        const combinedData = await btse.getCombinedData();
        console.log(`Получено объединенных данных: ${combinedData.length}`);
        console.log(combinedData);
        
    } catch (error) {
        console.error('Ошибка при тестировании:', error);
    }
}

// Запускаем тест
if (require.main === module) {
    testBtseAPI();
}

module.exports = { testBtseAPI };
