const { BtseAPI } = require('./btse_lib');

async function main() {

    const btse = new BtseAPI();

    try {
        
        // const fundings = await btse.getAllFundings();
        // console.log(`Получено фандинг-рейтов: ${fundings.length}`);
        

        // const prices = await btse.getAllFuturesPrices();
        // console.log(`Получено цен фьючерсов: ${prices.length}`);
        

        const combinedData = await btse.getCombinedData();
        console.log(combinedData);

    } catch (error) {
        console.error('Ошибка при тестировании:', error);
    }
}

// Запускаем тестирование
main().catch(console.error);

module.exports = { main };
