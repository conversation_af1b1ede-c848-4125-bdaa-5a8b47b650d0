const axios = require('axios');

const BTSE_API_URL = 'https://api.btse.com';

async function getBTSEFundingRates() {
    const btseInfo = [];
    try {
        // Добавляем параметр listFullAttributes=true для получения fundingTime
        const response = await axios.get(`${BTSE_API_URL}/api/v2.1/market_summary?listFullAttributes=true`);
        const markets = response.data;

        for (const data of markets) {
            // Фильтруем только фьючерсные контракты, у которых есть фандинг
            if (data.futures && data.fundingRate !== undefined) {
                btseInfo.push({
                    exchange: 'BTSE',
                    token: data.symbol,
                    fundingRate: data.fundingRate * 100, // Правильное название поля
                    nextFundingTime: data.fundingTime // Правильное название поля
                });
            }
        }
    } catch (error) {
        console.error('Ошибка при получении фандинга с BTSE:', error.message);
    }

    return btseInfo;
}

getBTSEFundingRates()

module.exports = {
    getBTSEFundingRates
};