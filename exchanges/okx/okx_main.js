const { OkxAPI } = require('./okx_lib');

async function main() {

    // Настройка использования прокси прямо в конструкторе
    // true - использовать прокси (с автоматическим fallback на обычные запросы если прокси не работают)
    // false - обычные запросы с вашего IP с задержкой 334мс
    const okx = new OkxAPI(true);

    try {
        const combinedData = await okx.getCombinedData();
        console.log(`Получено объединенных данных: ${combinedData.length}`);
        console.log(combinedData);

    } catch (error) {
        console.error('Ошибка при выполнении тестов:', error);
    }
}

main().catch(console.error);
