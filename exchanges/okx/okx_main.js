const { OkxAPI } = require('./okx_lib');

async function main() {
    
    const USE_PROXIES = true;  // true/false - включить/выключить использование прокси
    const TEST_PROXIES = true; // true/false - тестировать прокси перед использованием

    if (!USE_PROXIES) {
        console.log('=== РЕЖИМ БЕЗ ПРОКСИ ===\n');

        const okxWithoutProxy = new OkxAPI(false);

        try {
            console.log('Получение первых 5 фандинг-рейтов без прокси...');
            const pairs = await okxWithoutProxy.getAllPairsNames();
            console.log(`Найдено пар: ${pairs.length}`);

            // Тестируем только первые 5 пар для быстрой проверки
            const testPairs = pairs.slice(0, 5);
            const fundings = [];

            for (const pair of testPairs) {
                try {
                    const fundingInfo = await okxWithoutProxy.okx.fetchFundingRate(pair);
                    fundings.push({
                        exchange: 'OKX',
                        token: fundingInfo.symbol,
                        fundingRate: fundingInfo.info.fundingRate * 100,
                        nextFundingTime: fundingInfo.info.nextFundingTime
                    });
                    console.log(`✓ ${pair}: ${(fundingInfo.info.fundingRate * 100).toFixed(4)}%`);
                } catch (error) {
                    console.log(`✗ ${pair}: ${error.message}`);
                }

                // Пауза между запросами
                await new Promise(resolve => setTimeout(resolve, 334));
            }

            console.log(`\nПолучено фандинг-рейтов: ${fundings.length} из ${testPairs.length}`);

        } catch (error) {
            console.error('Ошибка при тестировании без прокси:', error);
        }

    } else {
        console.log('=== РЕЖИМ С ПРОКСИ ===\n');

        const okxWithProxy = new OkxAPI(USE_PROXIES, TEST_PROXIES);

        try {
            console.log('Получение всех фандинг-рейтов с использованием прокси...');
            const startTime = Date.now();

            const fundings = await okxWithProxy.getAllFundings();

            const endTime = Date.now();
            const duration = (endTime - startTime) / 1000;

            console.log(`\n=== РЕЗУЛЬТАТЫ ===`);
            console.log(`Получено фандинг-рейтов: ${fundings.length}`);
            console.log(`Время выполнения: ${duration.toFixed(2)} секунд`);

            if (fundings.length > 0) {
                console.log(`Скорость: ${(fundings.length / duration).toFixed(1)} запросов/сек`);
                console.log('\nПримеры:');
                fundings.slice(0, 5).forEach((funding, index) => {
                    console.log(`${index + 1}. ${funding.token}: ${funding.fundingRate.toFixed(4)}%`);
                });
            }

        } catch (error) {
            console.error('Ошибка при тестировании с прокси:', error);
        }
    }
}

// Запускаем тестирование
main().catch(console.error);
