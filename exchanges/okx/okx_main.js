const { OkxAPI } = require('./okx_lib');

async function main() {
    console.log('=== ТЕСТИРОВАНИЕ OKX API БЕЗ ПРОКСИ ===\n');

    // Тест без прокси
    const okxWithoutProxy = new OkxAPI(false);

    try {
        console.log('1. Тест без прокси - получение первых 5 фандинг-рейтов...');
        const pairs = await okxWithoutProxy.getAllPairsNames();
        console.log(`Найдено пар: ${pairs.length}`);

        // Тестируем только первые 5 пар для быстрой проверки
        const testPairs = pairs.slice(0, 5);
        const fundings = [];

        for (const pair of testPairs) {
            try {
                const fundingInfo = await okxWithoutProxy.okx.fetchFundingRate(pair);
                fundings.push({
                    exchange: 'OKX',
                    token: fundingInfo.symbol,
                    fundingRate: fundingInfo.info.fundingRate * 100,
                    nextFundingTime: fundingInfo.info.nextFundingTime
                });
                console.log(`✓ ${pair}: ${(fundingInfo.info.fundingRate * 100).toFixed(4)}%`);
            } catch (error) {
                console.log(`✗ ${pair}: ${error.message}`);
            }

            // Пауза между запросами
            await new Promise(resolve => setTimeout(resolve, 334));
        }

        console.log(`\nПолучено фандинг-рейтов: ${fundings.length} из ${testPairs.length}`);

    } catch (error) {
        console.error('Ошибка при тестировании без прокси:', error);
    }

    console.log('\n=== ТЕСТИРОВАНИЕ OKX API С ПРОКСИ ===\n');

    // Тест с прокси (только если прокси работают)
    const okxWithProxy = new OkxAPI(true);

    try {
        console.log('2. Тест с прокси - получение фандинг-рейтов...');
        const fundings = await okxWithProxy.getAllFundings();
        console.log(`Получено фандинг-рейтов с прокси: ${fundings.length}`);

        if (fundings.length > 0) {
            console.log('Примеры:');
            fundings.slice(0, 3).forEach((funding, index) => {
                console.log(`${index + 1}. ${funding.token}: ${funding.fundingRate.toFixed(4)}%`);
            });
        }

    } catch (error) {
        console.error('Ошибка при тестировании с прокси:', error);
    }
}

// Запускаем тестирование
main().catch(console.error);
