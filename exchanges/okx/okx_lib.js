const axios = require('axios');
const ccxt = require('ccxt');
const fs = require('fs');
const path = require('path');

class ProxyManager {
    constructor(proxiesFilePath) {
        this.proxies = [];
        this.workingProxies = [];
        this.currentIndex = 0;
        this.loadProxies(proxiesFilePath);
    }

    loadProxies(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n').filter(line => line.trim());

            this.proxies = lines.map(line => {
                const [host, port, username, password] = line.split(':');
                if (host && port && username && password) {
                    return {
                        host: host.trim(),
                        port: parseInt(port.trim()),
                        auth: {
                            username: username.trim(),
                            password: password.trim()
                        }
                    };
                }
                return null;
            }).filter(proxy => proxy !== null);

            console.log(`Загружено ${this.proxies.length} прокси`);
        } catch (error) {
            console.error('Ошибка при загрузке прокси:', error.message);
            this.proxies = [];
        }
    }

    getNextProxy() {
        if (this.proxies.length === 0) return null;

        const proxy = this.proxies[this.currentIndex];
        this.currentIndex = (this.currentIndex + 1) % this.proxies.length;
        return proxy;
    }

    getProxyConfig(proxy) {
        if (!proxy) return {};

        return {
            proxy: {
                protocol: 'http',
                host: proxy.host,
                port: proxy.port,
                auth: proxy.auth
            },
            httpsAgent: false, // Отключаем HTTPS agent для прокси
            timeout: 15000
        };
    }

    getAllProxies() {
        return [...this.proxies];
    }

    getWorkingProxies() {
        return [...this.workingProxies];
    }

    async testProxy(proxy, testUrl = 'https://www.okx.com/api/v5/public/time') {
        try {
            const proxyConfig = this.getProxyConfig(proxy);

            const options = {
                method: 'GET',
                url: testUrl,
                timeout: 2000, // Очень короткий таймаут для быстрого тестирования
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                validateStatus: () => true, // Принимаем любой статус
                ...proxyConfig
            };

            const response = await axios(options);
            // Считаем прокси рабочим если получили любой ответ (даже ошибку от сервера)
            return response.status >= 200 && response.status < 500;
        } catch (error) {
            // Прокси не работает только если нет соединения вообще
            return false;
        }
    }

    async testAllProxies(maxConcurrent = 50) {
        console.log(`Быстрое тестирование ${this.proxies.length} прокси...`);

        this.workingProxies = [];
        let testedCount = 0;
        let workingCount = 0;

        // Создаем семафор для ограничения одновременных запросов
        const semaphore = new Array(maxConcurrent).fill(null);

        const testPromises = this.proxies.map(async (proxy) => {
            // Ждем свободный слот в семафоре
            await new Promise(resolve => {
                const checkSlot = () => {
                    const freeIndex = semaphore.findIndex(slot => slot === null);
                    if (freeIndex !== -1) {
                        semaphore[freeIndex] = proxy;
                        resolve(freeIndex);
                    } else {
                        setTimeout(checkSlot, 1);
                    }
                };
                checkSlot();
            }).then(async (slotIndex) => {
                try {
                    const isWorking = await this.testProxy(proxy);

                    testedCount++;
                    if (isWorking) {
                        workingCount++;
                        this.workingProxies.push(proxy);
                    }

                    // Показываем прогресс каждые 20 прокси или в конце
                    if (testedCount % 20 === 0 || testedCount === this.proxies.length) {
                        console.log(`Протестировано: ${testedCount}/${this.proxies.length}, рабочих: ${workingCount}`);
                    }

                } finally {
                    // Освобождаем слот
                    semaphore[slotIndex] = null;
                }
            });
        });

        await Promise.all(testPromises);

        console.log(`Тестирование завершено. Рабочих прокси: ${this.workingProxies.length} из ${this.proxies.length}`);
        return this.workingProxies.length;
    }

    async testAllProxiesFast(maxConcurrent = 100) {
        console.log(`Сверхбыстрое тестирование ${this.proxies.length} прокси...`);

        this.workingProxies = [];
        let testedCount = 0;

        // Функция для обработки одного прокси
        const testSingleProxy = async (proxy) => {
            const isWorking = await this.testProxy(proxy);
            testedCount++;

            if (isWorking) {
                this.workingProxies.push(proxy);
            }

            // Показываем прогресс
            if (testedCount % 25 === 0 || testedCount === this.proxies.length) {
                console.log(`Протестировано: ${testedCount}/${this.proxies.length}, рабочих: ${this.workingProxies.length}`);
            }

            return isWorking;
        };

        // Создаем пул промисов с ограничением
        const executeWithLimit = async (tasks, limit) => {
            const results = [];
            const executing = [];

            for (const task of tasks) {
                const promise = task().then(result => {
                    executing.splice(executing.indexOf(promise), 1);
                    return result;
                });

                results.push(promise);
                executing.push(promise);

                if (executing.length >= limit) {
                    await Promise.race(executing);
                }
            }

            await Promise.all(results);
            return results;
        };

        // Создаем задачи для тестирования
        const tasks = this.proxies.map(proxy => () => testSingleProxy(proxy));

        await executeWithLimit(tasks, maxConcurrent);

        console.log(`Тестирование завершено. Рабочих прокси: ${this.workingProxies.length} из ${this.proxies.length}`);
        return this.workingProxies.length;
    }

    async testAllProxiesUltraFast() {
        console.log(`Молниеносное тестирование ${this.proxies.length} прокси...`);

        this.workingProxies = [];
        let testedCount = 0;

        // Тестируем все прокси одновременно без ограничений
        const testPromises = this.proxies.map(async (proxy) => {
            try {
                const isWorking = await this.testProxy(proxy);
                testedCount++;

                if (isWorking) {
                    this.workingProxies.push(proxy);
                }

                // Показываем прогресс каждые 30 прокси
                if (testedCount % 30 === 0) {
                    console.log(`Протестировано: ${testedCount}/${this.proxies.length}, рабочих: ${this.workingProxies.length}`);
                }

                return isWorking;
            } catch (error) {
                testedCount++;
                return false;
            }
        });

        await Promise.all(testPromises);

        console.log(`Тестирование завершено. Рабочих прокси: ${this.workingProxies.length} из ${this.proxies.length}`);
        return this.workingProxies.length;
    }
}

class OkxAPI {
    constructor(useProxies = true, testProxies = true, testMethod = 'ultra') {
        this.okx = new ccxt.okx({
            enableRateLimit: true,
            timeout: 30000, // Увеличиваем таймаут до 30 секунд
            options: {
                defaultType: 'swap' // Устанавливаем тип по умолчанию на фьючерсы
            }
        });
        this.baseUrl = 'https://www.okx.com';
        this.useProxies = useProxies;
        this.testProxies = testProxies;
        this.testMethod = testMethod; // 'normal', 'fast', 'ultra'
        this.proxiesReady = false;

        // Инициализируем менеджер прокси только если нужно
        if (useProxies) {
            const proxiesPath = path.join(__dirname, '..', 'proxies.txt');
            this.proxyManager = new ProxyManager(proxiesPath);
        }
    }

    async getAllPairsNames() {
        try {
            const options = {
                method: 'GET',
                url: `${this.baseUrl}/api/v5/public/instruments`,
                headers: {
                    'Content-Type': 'application/json',
                },
                params: {
                    instType: 'SWAP',
                },
            };
            const response = await axios(options);
            
            // Получение всех пар с типом SWAP
            return response.data.data.map(pair => pair.instId);
        } catch (error) {
            console.error('Ошибка при получении списка пар OKX:', error.message);
            return [];
        }
    }

    async getAllFundings() {
        try {
            const pairs = await this.getAllPairsNames();
            const delay = 334; // Интервал для соблюдения лимита (3 запроса в секунду)

            if (!this.useProxies || !this.proxyManager) {
                console.log('Прокси отключены, используем обычные запросы');
                return this.getAllFundingsSequential(pairs, delay);
            }

            // Проверяем и тестируем прокси если еще не делали
            if (!this.proxiesReady && this.testProxies) {
                let workingCount;

                switch (this.testMethod) {
                    case 'normal':
                        workingCount = await this.proxyManager.testAllProxies(10);
                        break;
                    case 'fast':
                        workingCount = await this.proxyManager.testAllProxiesFast(100);
                        break;
                    case 'ultra':
                    default:
                        workingCount = await this.proxyManager.testAllProxiesUltraFast();
                        break;
                }

                this.proxiesReady = true;

                if (workingCount === 0) {
                    console.log('Рабочих прокси не найдено, используем обычные запросы');
                    return this.getAllFundingsSequential(pairs, delay);
                }
            }

            const proxies = this.testProxies ?
                this.proxyManager.getWorkingProxies() :
                this.proxyManager.getAllProxies();

            if (proxies.length === 0) {
                console.log('Прокси не найдены, используем обычные запросы');
                return this.getAllFundingsSequential(pairs, delay);
            }

            console.log(`Используем ${proxies.length} ${this.testProxies ? 'протестированных' : ''} прокси для параллельных запросов`);
            return this.getAllFundingsParallel(pairs, proxies, delay);

        } catch (error) {
            console.error('Ошибка при получении фандинга с OKX:', error.message);
            console.log('Переключаемся на обычные запросы...');

            // Fallback на обычные запросы при любой ошибке
            try {
                const pairs = await this.getAllPairsNames();
                return this.getAllFundingsSequential(pairs, 334);
            } catch (fallbackError) {
                console.error('Ошибка при fallback запросах:', fallbackError.message);
                return [];
            }
        }
    }

    async getAllFundingsSequential(pairs, delay) {
        const result = [];

        for (let i = 0; i < pairs.length; i++) {
            try {
                const fundingInfo = await this.okx.fetchFundingRate(pairs[i]);

                result.push({
                    exchange: 'OKX',
                    token: fundingInfo.symbol,
                    fundingRate: fundingInfo.info.fundingRate * 100,
                    nextFundingTime: fundingInfo.info.nextFundingTime
                });
            } catch (error) {
                console.error(`Ошибка при обработке пары ${pairs[i]}:`, error.message);
            }

            // Пауза между запросами
            if (i < pairs.length - 1) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        return result;
    }

    async getAllFundingsParallel(pairs, proxies, delay) {
        const result = [];
        const batchSize = proxies.length;
        const batches = [];

        // Разбиваем пары на батчи по количеству прокси
        for (let i = 0; i < pairs.length; i += batchSize) {
            batches.push(pairs.slice(i, i + batchSize));
        }

        console.log(`Обрабатываем ${pairs.length} пар в ${batches.length} батчах по ${batchSize} запросов`);

        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            const batchPromises = [];

            // Создаем промисы для каждой пары в батче с соответствующим прокси
            for (let i = 0; i < batch.length; i++) {
                const pair = batch[i];
                const proxy = proxies[i % proxies.length];

                batchPromises.push(
                    this.fetchFundingRateWithProxy(pair, proxy)
                );
            }

            // Выполняем все запросы в батче параллельно
            const batchResults = await Promise.allSettled(batchPromises);

            // Обрабатываем результаты
            batchResults.forEach((promiseResult, index) => {
                if (promiseResult.status === 'fulfilled' && promiseResult.value) {
                    result.push(promiseResult.value);
                } else {
                    console.error(`Ошибка при обработке пары ${batch[index]}:`,
                        promiseResult.reason?.message || 'Неизвестная ошибка');
                }
            });

            console.log(`Обработан батч ${batchIndex + 1}/${batches.length}, получено ${result.length} результатов`);

            // Пауза между батчами
            if (batchIndex < batches.length - 1) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        return result;
    }

    async fetchFundingRateWithProxy(pair, proxy) {
        try {
            const proxyConfig = this.proxyManager.getProxyConfig(proxy);

            const options = {
                method: 'GET',
                url: `${this.baseUrl}/api/v5/public/funding-rate`,
                params: {
                    instId: pair
                },
                timeout: 15000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                ...proxyConfig
            };

            const response = await axios(options);

            if (response.data && response.data.data && response.data.data.length > 0) {
                const fundingData = response.data.data[0];
                return {
                    exchange: 'OKX',
                    token: pair,
                    fundingRate: parseFloat(fundingData.fundingRate) * 100,
                    nextFundingTime: fundingData.nextFundingTime
                };
            }

            return null;
        } catch (error) {
            // Более детальная обработка ошибок
            if (error.code === 'EPROTO' || error.message.includes('SSL')) {
                throw new Error(`SSL ошибка с прокси ${proxy.host}:${proxy.port}`);
            } else if (error.code === 'ECONNREFUSED') {
                throw new Error(`Прокси ${proxy.host}:${proxy.port} недоступен`);
            } else if (error.code === 'ETIMEDOUT') {
                throw new Error(`Таймаут прокси ${proxy.host}:${proxy.port}`);
            } else {
                throw new Error(`Прокси ${proxy.host}:${proxy.port} - ${error.message}`);
            }
        }
    }

    async getAllFuturesPrices() {
        try {
            // Используем fetchTickers для получения цен фьючерсов
            const tickers = await this.okx.fetchTickers(undefined, { type: 'swap' });
            const result = [];

            for (const [symbol, ticker] of Object.entries(tickers)) {
                // Фильтруем только фьючерсы (символы содержащие :USDT или подобные)
                if (symbol.includes(':USDT') || symbol.includes(':USD') || ticker.info) {
                    result.push({
                        exchange: 'OKX',
                        token: symbol,
                        lastPrice: ticker.last,
                        markPrice: ticker.markPrice || ticker.last,
                        indexPrice: ticker.indexPrice || ticker.last,
                        dailyChange: ticker.change,
                        dailyChangePercent: ticker.percentage,
                        volume24h: ticker.baseVolume,
                        quoteVolume24h: ticker.quoteVolume,
                        high24h: ticker.high,
                        low24h: ticker.low,
                        bid: ticker.bid,
                        ask: ticker.ask,
                        timestamp: ticker.timestamp,
                        openInterest: ticker.info?.openInterest || null
                    });
                }
            }

            return result;
        } catch (error) {
            console.error('Ошибка при получении цен фьючерсов с OKX:', error.message);
            return [];
        }
    }

    async getCombinedData() {
        try {
            const [fundings, prices] = await Promise.all([
                this.getAllFundings(),
                this.getAllFuturesPrices()
            ]);

            console.log(`Получено данных о фандингах: ${fundings.length}`);
            console.log(`Получено данных о ценах: ${prices.length}`);

            // Создаем Map с данными о ценах по токенам
            const pricesMap = new Map();
            prices.forEach(price => {
                pricesMap.set(price.token, price);
            });

            // Объединяем данные
            const combinedData = [];
            let matchedCount = 0;

            fundings.forEach(funding => {
                const priceData = pricesMap.get(funding.token);
                if (priceData) {
                    combinedData.push({
                        ...funding,
                        ...priceData
                    });
                    matchedCount++;
                } else {
                    // Добавляем данные о фандинге даже если нет цены
                    combinedData.push(funding);
                }
            });

            console.log(`Совпадений по токенам: ${matchedCount} из ${fundings.length}`);
            console.log(`Процент совпадений: ${((matchedCount / fundings.length) * 100).toFixed(1)}%`);

            return combinedData;
        } catch (error) {
            console.error('Ошибка при получении объединенных данных с OKX:', error.message);
            return [];
        }
    }
}

module.exports = { OkxAPI };
